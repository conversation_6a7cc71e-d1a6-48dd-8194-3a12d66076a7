<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Helper Message Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        [data-theme="dark"] body {
            background-color: #212529;
            color: #e9ecef;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        [data-theme="dark"] .container {
            background: #2d3035;
            color: #e9ecef;
        }
        
        .field-group {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        
        [data-theme="dark"] .field-group {
            border-color: #495057;
        }
        
        .field-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #495057;
        }
        
        [data-theme="dark"] .field-label {
            color: #adb5bd;
        }
        
        .field-value {
            font-size: 1.2rem;
            font-weight: 500;
            color: #212529;
            margin-bottom: 10px;
        }
        
        [data-theme="dark"] .field-value {
            color: #e9ecef;
        }
        
        /* Tooltip styling for rounding explanation */
        .rounding-tooltip-container {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            color: #6c757d;
        }

        .rounding-tooltip-icon {
            cursor: help;
            font-size: 0.9rem;
            color: #007bff;
            transition: color 0.2s ease;
        }

        .rounding-tooltip-icon:hover {
            color: #0056b3;
        }

        .tooltip-label {
            font-size: 0.75rem;
            color: #6c757d;
        }

        /* Dark mode support for tooltip */
        [data-theme="dark"] .rounding-tooltip-container {
            color: #adb5bd;
        }

        [data-theme="dark"] .rounding-tooltip-icon {
            color: #66b3ff;
        }

        [data-theme="dark"] .rounding-tooltip-icon:hover {
            color: #99ccff;
        }

        [data-theme="dark"] .tooltip-label {
            color: #adb5bd;
        }

        /* Custom tooltip styling */
        .tooltip .tooltip-inner {
            background-color: #2d3035;
            color: #e9ecef;
            border: 1px solid #495057;
            font-size: 0.75rem;
            max-width: 300px;
            text-align: right;
            direction: rtl;
        }

        .tooltip .tooltip-arrow::before {
            border-bottom-color: #2d3035;
        }

        /* Light mode tooltip */
        [data-theme="light"] .tooltip .tooltip-inner,
        body:not([data-theme]) .tooltip .tooltip-inner {
            background-color: #f8f9fa;
            color: #212529;
            border: 1px solid #e9ecef;
        }

        [data-theme="light"] .tooltip .tooltip-arrow::before,
        body:not([data-theme]) .tooltip .tooltip-arrow::before {
            border-bottom-color: #f8f9fa;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #007bff;
        }
        
        [data-theme="dark"] .demo-title {
            color: #66b3ff;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">
        <i class="bi bi-moon-fill"></i> Toggle Dark Mode
    </button>
    
    <div class="container">
        <h1>Helper Message Demo - Merit Report Rounding</h1>
        
        <div class="demo-section">
            <div class="demo-title">Monthly Contract Example</div>
            <div class="field-group">
                <div class="field-label">إجمالي مبلغ الاستحقاق قبل الخصومات</div>
                <div class="field-value">1,250.00</div>
                <div class="rounding-tooltip-container">
                    <i class="bi bi-info-circle rounding-tooltip-icon"
                       data-bs-toggle="tooltip"
                       data-bs-placement="bottom"
                       data-bs-html="true"
                       title="Rounding applied for monthly contract — Original value: 1250.30 → Rounded value: 1250.00">
                    </i>
                    <span class="tooltip-label">معلومات التقريب</span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">Monthly Contract (No Rounding Needed)</div>
            <div class="field-group">
                <div class="field-label">إجمالي مبلغ الاستحقاق قبل الخصومات</div>
                <div class="field-value">1,250.00</div>
                <div class="rounding-tooltip-container">
                    <i class="bi bi-info-circle rounding-tooltip-icon"
                       data-bs-toggle="tooltip"
                       data-bs-placement="bottom"
                       data-bs-html="true"
                       title="Monthly contract: Automatic rounding to nearest whole number">
                    </i>
                    <span class="tooltip-label">معلومات التقريب</span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">Daily Contract Example</div>
            <div class="field-group">
                <div class="field-label">إجمالي مبلغ الاستحقاق قبل الخصومات</div>
                <div class="field-value">1,250.75</div>
                <div class="rounding-tooltip-container">
                    <i class="bi bi-info-circle rounding-tooltip-icon"
                       data-bs-toggle="tooltip"
                       data-bs-placement="bottom"
                       data-bs-html="true"
                       title="Daily contract: Decimal values preserved (no rounding)">
                    </i>
                    <span class="tooltip-label">معلومات التقريب</span>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">Features</div>
            <ul>
                <li>✅ Tooltip on hover - no static text clutter</li>
                <li>✅ Small, unobtrusive info icon</li>
                <li>✅ Full dark mode support</li>
                <li>✅ Clear explanation of rounding rules</li>
                <li>✅ Different messages for monthly vs daily contracts</li>
                <li>✅ Responsive design</li>
                <li>✅ Consistent with existing UI styling</li>
                <li>✅ Bootstrap tooltip integration</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            if (newTheme === 'dark') {
                body.setAttribute('data-theme', 'dark');
            } else {
                body.removeAttribute('data-theme');
            }
        }
    </script>
</body>
</html>
