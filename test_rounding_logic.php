<?php
// Test file to verify the rounding logic for monthly contracts

// Helper function to apply rounding logic for monthly contracts
function applyMonthlyContractRounding($amount) {
    $decimalPart = $amount - floor($amount);
    
    if ($decimalPart <= 0.50) {
        // Round down (floor function)
        return floor($amount);
    } else {
        // Round up (ceiling function)
        return ceil($amount);
    }
}

// Test cases based on the requirements
$testCases = [
    // Test case 1: 1250.30 → 1250 (rounded down)
    ['input' => 1250.30, 'expected' => 1250, 'description' => '1250.30 should round down to 1250'],
    
    // Test case 2: 1250.60 → 1251 (rounded up)
    ['input' => 1250.60, 'expected' => 1251, 'description' => '1250.60 should round up to 1251'],
    
    // Test case 3: 1250.50 → 1250 (rounded down, as ≤ 0.50)
    ['input' => 1250.50, 'expected' => 1250, 'description' => '1250.50 should round down to 1250'],
    
    // Additional test cases
    ['input' => 1250.49, 'expected' => 1250, 'description' => '1250.49 should round down to 1250'],
    ['input' => 1250.51, 'expected' => 1251, 'description' => '1250.51 should round up to 1251'],
    ['input' => 1250.00, 'expected' => 1250, 'description' => '1250.00 should remain 1250'],
    ['input' => 1250.99, 'expected' => 1251, 'description' => '1250.99 should round up to 1251'],
    ['input' => 1250.01, 'expected' => 1250, 'description' => '1250.01 should round down to 1250'],
];

echo "<h2>Testing Monthly Contract Rounding Logic</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
echo "<tr><th>Input</th><th>Expected</th><th>Actual</th><th>Result</th><th>Description</th></tr>\n";

$allPassed = true;

foreach ($testCases as $test) {
    $input = $test['input'];
    $expected = $test['expected'];
    $actual = applyMonthlyContractRounding($input);
    $passed = ($actual === $expected);
    
    if (!$passed) {
        $allPassed = false;
    }
    
    $resultColor = $passed ? 'green' : 'red';
    $resultText = $passed ? 'PASS' : 'FAIL';
    
    echo "<tr>\n";
    echo "<td>{$input}</td>\n";
    echo "<td>{$expected}</td>\n";
    echo "<td>{$actual}</td>\n";
    echo "<td style='color: {$resultColor}; font-weight: bold;'>{$resultText}</td>\n";
    echo "<td>{$test['description']}</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

if ($allPassed) {
    echo "<h3 style='color: green;'>✅ All tests passed! The rounding logic is working correctly.</h3>\n";
} else {
    echo "<h3 style='color: red;'>❌ Some tests failed. Please check the rounding logic.</h3>\n";
}

// Test JavaScript equivalent
echo "<h2>JavaScript Rounding Logic Test</h2>\n";
echo "<script>\n";
echo "function applyMonthlyContractRounding(amount) {\n";
echo "    const decimalPart = amount - Math.floor(amount);\n";
echo "    \n";
echo "    if (decimalPart <= 0.50) {\n";
echo "        return Math.floor(amount);\n";
echo "    } else {\n";
echo "        return Math.ceil(amount);\n";
echo "    }\n";
echo "}\n";
echo "\n";
echo "// Test the JavaScript function\n";
echo "const jsTestCases = [\n";
foreach ($testCases as $test) {
    echo "    {input: {$test['input']}, expected: {$test['expected']}, description: '{$test['description']}'},\n";
}
echo "];\n";
echo "\n";
echo "console.log('JavaScript Rounding Logic Test Results:');\n";
echo "let jsAllPassed = true;\n";
echo "jsTestCases.forEach(test => {\n";
echo "    const actual = applyMonthlyContractRounding(test.input);\n";
echo "    const passed = actual === test.expected;\n";
echo "    if (!passed) jsAllPassed = false;\n";
echo "    console.log(`Input: \${test.input}, Expected: \${test.expected}, Actual: \${actual}, Result: \${passed ? 'PASS' : 'FAIL'} - \${test.description}`);\n";
echo "});\n";
echo "\n";
echo "if (jsAllPassed) {\n";
echo "    console.log('✅ All JavaScript tests passed!');\n";
echo "} else {\n";
echo "    console.log('❌ Some JavaScript tests failed.');\n";
echo "}\n";
echo "</script>\n";
?>
