# Merit Report Automatic Rounding Implementation

## Overview
This document describes the implementation of automatic rounding logic for the "Total Merit Amount Before Deductions" field in the merit report creation process, specifically for monthly contracts.

## Requirements Implemented
- **Monthly contracts**: Automatically round the "Total Merit Amount Before Deductions" to the nearest whole number
- **Daily contracts**: Allow decimal values (no rounding applied)
- **Rounding rules**: 
  - If decimal portion ≤ 0.50: round down (floor function)
  - If decimal portion > 0.50: round up (ceiling function)

## Implementation Details

### 1. PHP Server-Side Implementation

#### Helper Function
```php
function applyMonthlyContractRounding($amount) {
    $decimalPart = $amount - floor($amount);
    
    if ($decimalPart <= 0.50) {
        return floor($amount);  // Round down
    } else {
        return ceil($amount);   // Round up
    }
}
```

#### Integration Point
- **File**: `Pages\create_merit_reports__con.php`
- **Location**: Lines 366-389 in the `save_merit_report` action
- **Process**: 
  1. Calculate base total: `$calculated_total = $actual_working_days * $today_wage`
  2. Query contract type from database
  3. Apply rounding if contract_type = 1 (monthly)
  4. Store rounded value in database

### 2. JavaScript Client-Side Implementation

#### Helper Functions
```javascript
function getContractType() {
    // Detects contract type from UI elements or PHP data
    // Returns: 1 for monthly, 2 for daily
}

function applyMonthlyContractRounding(amount) {
    const decimalPart = amount - Math.floor(amount);
    
    if (decimalPart <= 0.50) {
        return Math.floor(amount);  // Round down
    } else {
        return Math.ceil(amount);   // Round up
    }
}
```

#### Integration Point
- **Function**: `updateTotalAmount()`
- **Process**:
  1. Calculate original total
  2. Check contract type
  3. Apply rounding if monthly contract
  4. Update UI with rounded value
  5. Show/hide rounding indicator

### 3. User Interface Enhancements

#### Helper Message
- **Element**: `#rounding-helper-message`
- **Purpose**: Explains rounding logic to users
- **Content**:
  - **Monthly contracts**: "العقود الشهرية: يتم تقريب المبلغ تلقائياً إلى أقرب رقم صحيح"
  - **Daily contracts**: "العقود اليومية: يتم الاحتفاظ بالقيم العشرية كما هي دون تقريب"
- **Styling**:
  - Small font size (0.75rem)
  - Light gray background with subtle border
  - Full dark mode support
  - Responsive design for mobile devices

#### Rounding Indicator
- **Element**: `#rounding-indicator`
- **Behavior**:
  - Shows when rounding is applied to monthly contracts
  - Displays original value → rounded value
  - Hidden for daily contracts or when no rounding needed

#### Visual Feedback
- Blue information box with border
- Shows original and rounded values
- Responsive to dark/light theme

## Test Cases

| Input    | Expected | Contract Type | Description                    |
|----------|----------|---------------|--------------------------------|
| 1250.30  | 1250     | Monthly       | Round down (≤ 0.50)           |
| 1250.60  | 1251     | Monthly       | Round up (> 0.50)             |
| 1250.50  | 1250     | Monthly       | Round down (= 0.50)           |
| 1250.75  | 1250.75  | Daily         | No rounding applied            |

## Files Modified

1. **Pages\create_merit_reports__con.php**
   - Added PHP rounding function
   - Updated server-side calculation logic
   - Added JavaScript rounding functions
   - Enhanced UI with rounding indicator
   - Added CSS styling for indicator

## Usage

### For Monthly Contracts
1. User sees helper message explaining automatic rounding rules
2. User enters/calculates merit amount
3. System automatically applies rounding
4. UI shows rounding indicator with original → rounded values
5. Rounded value is saved to database

### For Daily Contracts
1. User sees helper message explaining decimal preservation
2. User enters/calculates merit amount
3. No rounding applied
4. Decimal values preserved
5. Original value saved to database

## Technical Notes

- **Database Impact**: Only the final rounded value is stored
- **Backward Compatibility**: Existing daily contracts unaffected
- **Performance**: Minimal overhead (single database query for contract type)
- **Validation**: Both client-side and server-side validation ensure consistency

## Testing

A test file `test_rounding_logic.php` has been created to verify the rounding logic with various test cases. The test covers both PHP and JavaScript implementations to ensure consistency.

## Future Enhancements

1. **Configuration**: Make rounding rules configurable per contract type
2. **Audit Trail**: Log original vs rounded values for audit purposes
3. **Bulk Operations**: Apply rounding logic to bulk merit report operations
4. **Reporting**: Add rounding information to merit report exports
